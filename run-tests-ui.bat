@echo off
echo Starting WARNO Mod Maker E2E Test Suite with UI...
echo.

REM Compile the test classes if needed
if not exist "src\test\java\com\warnomodmaker\*.class" (
    echo Compiling test classes...
    javac -cp "lib\*;src" -d src src\test\java\com\warnomodmaker\*.java
    if errorlevel 1 (
        echo Compilation failed!
        pause
        exit /b 1
    )
)

REM Run the tests with UI
java -Xmx4g -cp "lib\*;src" test.java.com.warnomodmaker.ComprehensiveE2ETest --ui

pause
