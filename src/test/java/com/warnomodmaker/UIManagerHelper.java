package test.java.com.warnomodmaker;

import javax.swing.UIManager;

/**
 * Helper class for UIManager operations to avoid method name conflicts
 */
public class UIManagerHelper {
    
    /**
     * Sets the system look and feel
     * @return true if successful, false otherwise
     */
    public static boolean setSystemLookAndFeel() {
        try {
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Sets a specific look and feel
     * @param lookAndFeelClassName the class name of the look and feel
     * @return true if successful, false otherwise
     */
    public static boolean setLookAndFeel(String lookAndFeelClassName) {
        try {
            UIManager.setLookAndFeel(lookAndFeelClassName);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Gets the system look and feel class name
     * @return the system look and feel class name
     */
    public static String getSystemLookAndFeelClassName() {
        return UIManager.getSystemLookAndFeel();
    }
}
