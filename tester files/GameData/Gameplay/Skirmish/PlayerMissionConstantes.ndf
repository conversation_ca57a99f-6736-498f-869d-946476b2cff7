

///////////////////////////////////////////////////////////////////////////////////////////
ETargetFilter_None     is 0
ETargetFilter_HasShot  is 1
ETargetFilter_UnitSeen is 2
///////////////////////////////////////////////////////////////////////////////////////////
private player_mission_BasicAirstrikeSupport is
[
    (["Avion_Chasseur_Bombardier", "Avion_Bombardier", "Avion_AT"], 2),
]

///////////////////////////////////////////////////////////////////////////////////////////
// MACRO ACTIONS
///////////////////////////////////////////////////////////////////////////////////////////
export playerMissionAttackDescriptor is TMacroActionDescriptor_ObjectiveAttack_Specific
(
    ParametresDeMission =
    [
        TGenerationSettingHolder(GenerationType = TGenerationType_FixedSize NumberLaunchBasedOnGenerationType = 1),
        TAirStrikeSettingHolder(Setting = ~/BasicAirstrikeSupport),
        TArtilleryAmountSettingHolder(ArtilleryAmount = 2),
        TBlitzkriegSettingHolder(RatioFactorToDisableSelfPreservation = 2.0   MinimumCostForSelfPreservation = 100),
        TAttackRatioSettingHolder(RatioMinimumPourAttaquer = 0.001),
        TMacroActionProductionSettingHolder(DisableReinforcement = true),
        TMacroActionLifetimeSettingHolder(MaintainForever = true), //IMPORTANT
        TDisableUnitToReleaseSettingHolder(),
    ]
)
///////////////////////////////////////////////////////////////////////////////////////////
export playerMissionDefenseDescriptor is TMacroActionDescriptor_Defense_Specific
(
    ParametresDeMission =
    [
        TGenerationSettingHolder(GenerationType = TGenerationType_FixedSize NumberLaunchBasedOnGenerationType = 1),
        TAirStrikeSettingHolder(Setting = ~/BasicAirstrikeSupport),
        TArtilleryAmountSettingHolder(ArtilleryAmount = 1),
        TMacroActionProductionSettingHolder(DisableReinforcement = true),
        TMacroActionLifetimeSettingHolder(MaintainForever = true), //IMPORTANT
        TDisableUnitToReleaseSettingHolder(),
    ]
)
///////////////////////////////////////////////////////////////////////////////////////////
export playerMissionArtilleryCounterBatteryDescriptor is TMacroActionDescriptor_Artillery_Specific
(
    ParametresDeMission =
    [
        TGenerationSettingHolder(GenerationType = TGenerationType_FixedSize NumberLaunchBasedOnGenerationType = 1),
        TArtilleryPositionningSettingHolder(ArtilleryPositionOffsetGRU = 3534),
        TMacroActionProductionSettingHolder(PoolModelList = [] DistanceToOptimumStartDuringDeploymentGRU = 1060),
        TMacroActionLifetimeSettingHolder(MaintainForever = true), //IMPORTANT
        TArtilleryPlayerMissionSettingHolder(
            AuthorizedTargetTags = ["Artillerie_MLRS", "Artillerie_Longue_Portee", "Artillerie_Courte_Portee"] TargetFilter = ETargetFilter_HasShot MaxDurationForTargetValidity = 60 MinDurationForTargetValidity = 2),
        TArtilleryStrikeSettingHolder(SupportStrikeRatio = 0.3 DelaytBetweenTargetDeathAndNewStrike = 3),
        TDisableUnitToReleaseSettingHolder(),
    ]
)
///////////////////////////////////////////////////////////////////////////////////////////
export playerMissionArtilleryDescriptor is TMacroActionDescriptor_Artillery_Specific
(
    ParametresDeMission =
    [
        TGenerationSettingHolder(GenerationType = TGenerationType_FixedSize NumberLaunchBasedOnGenerationType = 1),
        TArtilleryPositionningSettingHolder(ArtilleryPositionOffsetGRU = 3534),
        TMacroActionProductionSettingHolder(PoolModelList = [] DistanceToOptimumStartDuringDeploymentGRU = 1060),
        TMacroActionLifetimeSettingHolder(MaintainForever = true), //IMPORTANT
        TArtilleryPlayerMissionSettingHolder(AuthorizedTargetTags = ["AllUnits"] TargetFilter = ETargetFilter_UnitSeen MinDurationForTargetValidity = 5),
        TArtilleryStrikeSettingHolder(SupportStrikeRatio = 0.3 DelaytBetweenTargetDeathAndNewStrike = 3),
        TDisableUnitToReleaseSettingHolder(),
    ]
)
///////////////////////////////////////////////////////////////////////////////////////////
export playerMissionAirstrikeDescriptor is TMacroActionDescriptor_AirStrike_Common
(
    ParametresDeMission =
    [
        TGenerationSettingHolder(GenerationType = TGenerationType_FixedSize NumberLaunchBasedOnGenerationType = 1),
        TMacroActionProductionSettingHolder(ProdProperties = [TProdProperty_LaunchAsSoonAsPossible] PoolModelList = []),
        TMacroActionLifetimeSettingHolder(MaintainForever = true), //IMPORTANT
        TAirStrikeSettingHolder(TypesDeRequetesAcceptees = ~/EAirStrikeRequestType/All),
        TAirStrikeAcceptableThreatSettingHolder(PercentAcceptableDangerousIndicesForAirplaneValidTarget = 0.5),
        TDisableUnitToReleaseSettingHolder(),
    ]
)
///////////////////////////////////////////////////////////////////////////////////////////
export playerMissionAirstrikeSEADDescriptor is TMacroActionDescriptor_AirStrike_Common
(
    ParametresDeMission =
    [
        TGenerationSettingHolder(GenerationType = TGenerationType_FixedSize NumberLaunchBasedOnGenerationType = 1),
        TMacroActionProductionSettingHolder(ProdProperties = [TProdProperty_LaunchAsSoonAsPossible] PoolModelList = []),
        TMacroActionLifetimeSettingHolder(MaintainForever = true), //IMPORTANT
        TAirStrikeSettingHolder(TypesDeRequetesAcceptees = ~/EAirStrikeRequestType/All),
        TDisableUnitToReleaseSettingHolder(),
    ]
)
///////////////////////////////////////////////////////////////////////////////////////////
export playerMissionAirRecoDescriptor is TMacroActionDescriptor_AirReco_Specific
(
    ParametresDeMission =
    [
        TGenerationSettingHolder(GenerationType = TGenerationType_FixedSize NumberLaunchBasedOnGenerationType = 1),
        TMacroActionLifetimeSettingHolder(MaintainForever = true), //IMPORTANT
        AirRecoMissionLaunchSettingHolderTemplate(MaxAirplaneCountOnBattlefield = 1000 TicksBetweenTwoLaunch = 0 TicksBetweenTwoMissions = 0),
        TDisableUnitToReleaseSettingHolder(),
    ]
)
///////////////////////////////////////////////////////////////////////////////////////////
export playerMissionSupplyDescriptor is TMacroActionDescriptor_Supply_Specific
(
    ParametresDeMission =
    [
        TGenerationSettingHolder(GenerationType = TGenerationType_FixedSize NumberLaunchBasedOnGenerationType = 1),
        TSupplySettingHolder(SupplyableMinimumRatio = MAP[
            (ESupplyableType_Fuel, 0.3),
            (ESupplyableType_Weapons, 0.3),
            (ESupplyableType_Life, 0.5),
        ]),
        TMacroActionLifetimeSettingHolder(MaintainForever = true), //IMPORTANT
        TDisableUnitToReleaseSettingHolder(),
    ]
)
///////////////////////////////////////////////////////////////////////////////////////////
// MISSION DESCRIPTORS
///////////////////////////////////////////////////////////////////////////////////////////
export PlayerMissionDescriptorAttack is TPlayerMissionDescriptor(
    MacroActionDescriptor = playerMissionAttackDescriptor
    AuthorizedTags = ~/Attack_AuthorizedTags
    ExclusionTags = ~/Attack_ExcludedTags
    Mergeable = true
    UsePosition = true
)

export PlayerMissionDescriptor_ManageArtillery_Focus is TPlayerMissionDescriptor(
    MacroActionDescriptor = playerMissionArtilleryDescriptor
    AuthorizedTags = ~/ManageArtillery_Focus_AuthorizedTags
    ExclusionTags = ~/ManageArtillery_Focus_ExcludedTags
    Mergeable = false
    UsePosition = true
)

export PlayerMissionDescriptor_Hold is TPlayerMissionDescriptor(
    MacroActionDescriptor = playerMissionDefenseDescriptor
    AuthorizedTags = ~/Hold_AuthorizedTags
    ExclusionTags = ~/Hold_ExcludedTags
    Mergeable = true
    UsePosition = true
)

export PlayerMissionDescriptor_ManageArtillery_CounterBattery is TPlayerMissionDescriptor(
    MacroActionDescriptor = playerMissionArtilleryCounterBatteryDescriptor
    AuthorizedTags = ~/ManageArtillery_CounterBattery_AuthorizedTags
    ExclusionTags = ~/ManageArtillery_CounterBattery_ExcludedTags
    Mergeable = false
    UsePosition = false
)

export PlayerMissionDescriptor_ManageArtillery_Auto is TPlayerMissionDescriptor(
    MacroActionDescriptor = playerMissionArtilleryDescriptor
    AuthorizedTags = ~/ManageArtillery_Auto_AuthorizedTags
    ExclusionTags = ~/ManageArtillery_Auto_ExcludedTags
    Mergeable = false
    UsePosition = false
)

export PlayerMissionDescriptor_ManageAirplanes is TPlayerMissionDescriptor(
    MacroActionDescriptor = playerMissionAirstrikeDescriptor
    AuthorizedTags = ~/ManageAirplanes_AuthorizedTags
    ExclusionTags = ~/ManageAirplanes_ExcludedTags
    Mergeable = false
    UsePosition = true
)

export PlayerMissionDescriptor_ManageAirplanesSEAD is TPlayerMissionDescriptor(
    MacroActionDescriptor = playerMissionAirstrikeSEADDescriptor
    AuthorizedTags = ~/ManageAirplanesSEAD_AuthorizedTags
    ExclusionTags = ~/ManageAirplanesSEAD_ExcludedTags
    Mergeable = false
    UsePosition = true
)


export PlayerMissionDescriptor_ManageAirReco is TPlayerMissionDescriptor(
    MacroActionDescriptor = playerMissionAirRecoDescriptor
    AuthorizedTags = ~/ManageAirReco_AuthorizedTags
    ExclusionTags = ~/ManageAirReco_ExcludedTags
    Mergeable = true
    UsePosition = true
)

export PlayerMissionDescriptor_Supply is TPlayerMissionDescriptor(
    MacroActionDescriptor = playerMissionSupplyDescriptor
    AuthorizedTags = ~/Supply_AuthorizedTags
    ExclusionTags = ~/Supply_ExcludedTags
    Mergeable = false
    UsePosition = false
)

export PlayerMissionDescriptor_Stop is TPlayerMissionDescriptor(
    MacroActionDescriptor = nil
    AuthorizedTags = ~/Stop_AuthorizedTags
    ExclusionTags = ~/Stop_ExcludedTags
    Mergeable = false
    UsePosition = false
)

///////////////////////////////////////////////////////////////////////////////////////////
// CONSTANTES
///////////////////////////////////////////////////////////////////////////////////////////
export PlayerMissionDescriptorList is TPlayerMissionConstantesDescriptor
(
    PlayerMissionDescriptorByType = MAP[
        (
            EPlayerMissionRequestType_Seize,
            TPlayerMissionOrderDescriptor(
                DescriptorPriority = [
                    PlayerMissionDescriptorAttack,
                ]
                DebugName = "Seize"
                UseMousePolicy = true
            )
        ),
        (
            EPlayerMissionRequestType_ManageArtillery_Focus,
            TPlayerMissionOrderDescriptor(
                DescriptorPriority = [
                    PlayerMissionDescriptor_ManageArtillery_Focus,
                ]
                DebugName = "ManageArtillery_Focus"
                UseMousePolicy = true
            )
        ),
        (
            EPlayerMissionRequestType_Hold,
            TPlayerMissionOrderDescriptor(
                DescriptorPriority = [
                    PlayerMissionDescriptor_Hold,
                ]
                DebugName = "Hold"
                UseMousePolicy = false
            )
        ),
        (
            EPlayerMissionRequestType_ManageArtillery_CounterBattery,
            TPlayerMissionOrderDescriptor(
                DescriptorPriority = [
                    PlayerMissionDescriptor_ManageArtillery_CounterBattery,
                ]
                DebugName = "ManageArtillery_CounterBattery"
                UseMousePolicy = false
            )
        ),
        (
            EPlayerMissionRequestType_ManageArtillery_Auto,
            TPlayerMissionOrderDescriptor(
                DescriptorPriority = [
                    PlayerMissionDescriptor_ManageArtillery_Auto,
                ]
                DebugName = "ManageArtillery_Auto"
                UseMousePolicy = false
            )
        ),
        (
            EPlayerMissionRequestType_ManageAirplanes,
            TPlayerMissionOrderDescriptor(
                DescriptorPriority = [
                    PlayerMissionDescriptor_ManageAirReco,
                    PlayerMissionDescriptor_ManageAirplanesSEAD,
                    PlayerMissionDescriptor_ManageAirplanes,
                ]
                DebugName = "Manage Airplanes"
                UseMousePolicy = false
            )
        ),
        (
            EPlayerMissionRequestType_Stop,
            TPlayerMissionOrderDescriptor(
                DescriptorPriority = [
                    PlayerMissionDescriptor_Stop,
                ]
                DebugName = "Stop Managing"
                UseMousePolicy = false
            )
        ),
        (
            EPlayerMissionRequestType_Supply,
            TPlayerMissionOrderDescriptor(
                DescriptorPriority = [
                    PlayerMissionDescriptor_Supply,
                ]
                DebugName = "Supply"
                UseMousePolicy = false
            )
        ),
    ]

    MaxCustomZoneDistanceForSimilarGRU = 293
)
///////////////////////////////////////////////////////////////////////////////////////////
// FEEDBACK
///////////////////////////////////////////////////////////////////////////////////////////
PlayerMissionFeedbackDescriptor is TUIPlayerMissionConstantesDescriptor
(

    PlayerMissionFeedbackDescriptorByType = MAP[
        (
            EPlayerMissionRequestType_Seize,
            TFeedbackStyleDescriptor(
                             Thickness = 4000.0
                             Color = [221, 30, 30, 180]
                         )
        ),
        (
            EPlayerMissionRequestType_ManageArtillery_Focus,
            TFeedbackStyleDescriptor(
                             Thickness = 4000.0
                             Color = [221, 30, 30, 180]
                         )
        ),
        (
            EPlayerMissionRequestType_Hold,
            TFeedbackStyleDescriptor(
                             Thickness = 4000.0
                             Color = [30, 30, 222, 180]
                         )
        ),
    ]

    StartFadeAlphaAfterTimeInSeconds = 0.5 // secondes
    AlphaFadeDurationInSeconds = 2.0 // secondes
)
