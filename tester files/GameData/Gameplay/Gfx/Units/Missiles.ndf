
private template template_PropulsionActionHappening
[
    Action
]
is TIntroduceMobileHappening
(
    Anchor = 'fx_fumee_missile'
    Happening = TSequencingActionHappening
    (
        ActionDescriptor = TActionCall( Action = <Action> )
    )
)

template Template_DescriptorMissileBase
[
    ProjectileModelResource,
    Actions,
]
is TTimelyDepictionReceiverFactory
(
    DepictionDescriptor = TDepictionDescriptor
    (
        ResidentMaterialTags = [ MaterialTagMech ]
        AdditionalTextures = $/M3D/Shader/VehicleDestTextures
        Selector = TConstantAlternativeSelector(Key = [LOD_High])
        DepictionAlternatives = [
            DepictionVisual_LOD_High( MeshDescriptor = <ProjectileModelResource> ),
        ]
        Actions = <Actions>

        Operators = [ TCosmeticMissilePropulsionFXOperatorDescriptor() ]
    )
)
